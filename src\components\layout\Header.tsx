'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession, signOut } from 'next-auth/react';
import { motion } from 'framer-motion';
import { Menu, X, User, BookOpen, Briefcase, Users, Calendar, LogOut, Settings } from 'lucide-react';

export function Header() {
  const { data: session, status } = useSession();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  // Get role-appropriate navigation items
  const getNavigation = () => {
    const userRole = (session?.user as any)?.role;

    // Base navigation for all authenticated users
    const baseNavigation = [
      { name: 'Learning Hub', href: '/courses', icon: BookOpen },
      { name: 'Events', href: '/events', icon: Calendar },
    ];

    // Role-specific navigation
    const roleNavigation = {
      student: [
        { name: 'Mentorship', href: '/mentorship', icon: Users },
        { name: 'Jobs', href: '/jobs', icon: Briefcase },
        { name: 'Community', href: '/community', icon: Users },
      ],
      mentor: [
        { name: 'Mentorship', href: '/mentorship', icon: Users },
        { name: 'Community', href: '/community', icon: Users },
      ],
      employer: [
        { name: 'Jobs', href: '/jobs', icon: Briefcase },
        { name: 'Community', href: '/community', icon: Users },
      ],
      partner: [
        { name: 'Community', href: '/community', icon: Users },
        { name: 'Resources', href: '/resources', icon: BookOpen },
      ],
      admin: [
        { name: 'Mentorship', href: '/mentorship', icon: Users },
        { name: 'Jobs', href: '/jobs', icon: Briefcase },
        { name: 'Community', href: '/community', icon: Users },
        { name: 'Resources', href: '/resources', icon: BookOpen },
      ],
    };

    if (!session) {
      // Public navigation for unauthenticated users
      return [
        { name: 'About', href: '/about', icon: Users },
      ];
    }

    return [...baseNavigation, ...(roleNavigation[userRole as keyof typeof roleNavigation] || [])];
  };

  const navigation = getNavigation();

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' });
  };

  return (
    <header className="fixed top-0 w-full z-50 px-2 sm:px-4 pt-2 sm:pt-4">
      {/* Muted Background Blobs */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-4 left-1/4 w-32 h-32 bg-gradient-to-r from-blue-600/3 to-purple-600/3 rounded-full filter blur-2xl animate-blob-drift" />
        <div className="absolute top-8 right-1/3 w-24 h-24 bg-gradient-to-r from-purple-600/2 to-pink-600/2 rounded-full filter blur-xl animate-blob-drift-reverse" />
        <div className="absolute top-2 left-2/3 w-20 h-20 bg-gradient-to-r from-pink-600/2 to-blue-600/2 rounded-full filter blur-xl animate-blob-float" />
      </div>

      <nav className="max-w-6xl mx-auto glassmorphic glow-border rounded-xl sm:rounded-2xl relative">
        <div className="nav-container">
          <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <div className="relative">
              <Image
                src="/logo.png"
                alt="Nova Logo"
                width={32}
                height={32}
                className="transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute -top-1 -right-1 w-2.5 h-2.5 bg-gradient-to-r from-blue-500 to-pink-600 rounded-full animate-twinkle" />
            </div>
            <span className="text-xl font-bold text-white glow-text font-orbitron">Nova</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center space-x-2 text-gray-300 hover:text-blue-400 transition-all duration-300 hover:glow-text group"
                >
                  <Icon className="h-4 w-4 group-hover:animate-float" />
                  <span className="font-medium">{item.name}</span>
                </Link>
              );
            })}
          </div>

          {/* Auth Section */}
          <div className="hidden md:flex items-center space-x-4">
            {session ? (
              <div className="relative">
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-2 text-gray-300 hover:text-blue-400 transition-all duration-300 group"
                >
                  <div className="h-8 w-8 bg-gradient-to-r from-blue-600 to-pink-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                    {session.user.name?.charAt(0).toUpperCase()}
                  </div>
                  <span className="font-medium">{session.user.name}</span>
                </button>

                {showUserMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 mt-2 w-48 glassmorphic glow-border rounded-lg py-2 z-50"
                  >
                    <Link
                      href="/dashboard"
                      className="block px-4 py-2 text-sm text-gray-300 hover:text-blue-400 hover:bg-white/5 transition-all duration-200"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <User className="inline h-4 w-4 mr-2" />
                      Dashboard
                    </Link>
                    <Link
                      href="/profile"
                      className="block px-4 py-2 text-sm text-gray-300 hover:text-blue-400 hover:bg-white/5 transition-all duration-200"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <Settings className="inline h-4 w-4 mr-2" />
                      Profile
                    </Link>
                    <button
                      onClick={handleSignOut}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-pink-400 hover:bg-white/5 transition-all duration-200"
                    >
                      <LogOut className="inline h-4 w-4 mr-2" />
                      Sign Out
                    </button>
                  </motion.div>
                )}
              </div>
            ) : (
              <>
                <Link
                  href="/auth/signin"
                  className="text-gray-300 hover:text-blue-400 transition-all duration-300 font-medium hover:glow-text"
                >
                  Sign In
                </Link>
                <Link
                  href="/auth/signup"
                  className="bg-gradient-to-r from-blue-600 to-pink-600 text-white px-6 py-3 rounded-lg hover:from-pink-600 hover:to-purple-600 transition-all duration-300 flex items-center space-x-2 font-medium"
                >
                  <User className="h-4 w-4" />
                  <span>Get Started</span>
                </Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-md text-gray-300 hover:text-blue-400 hover:bg-white/5 transition-all duration-300 glassmorphic"
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="md:hidden py-4 border-t border-white/20"
            >
              <div className="nav-container">
                <div className="flex flex-col space-y-4">
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="flex items-center space-x-2 text-gray-300 hover:text-blue-400 transition-all duration-300 px-2 py-2 rounded-lg hover:bg-white/5"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Icon className="h-4 w-4" />
                    <span className="font-medium">{item.name}</span>
                  </Link>
                );
              })}
              <div className="flex flex-col space-y-3 pt-4 border-t border-white/20">
                {session ? (
                  <>
                    <Link
                      href="/dashboard"
                      className="flex items-center space-x-2 text-gray-300 hover:text-blue-400 transition-all duration-300 px-2 py-2 font-medium"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <User className="h-4 w-4" />
                      <span>Dashboard</span>
                    </Link>
                    <Link
                      href="/profile"
                      className="flex items-center space-x-2 text-gray-300 hover:text-blue-400 transition-all duration-300 px-2 py-2 font-medium"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <Settings className="h-4 w-4" />
                      <span>Profile</span>
                    </Link>
                    <button
                      onClick={() => {
                        handleSignOut();
                        setIsMenuOpen(false);
                      }}
                      className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-all duration-300 px-2 py-2 font-medium text-left"
                    >
                      <LogOut className="h-4 w-4" />
                      <span>Sign Out</span>
                    </button>
                  </>
                ) : (
                  <>
                    <Link
                      href="/auth/signin"
                      className="text-gray-300 hover:text-blue-400 transition-all duration-300 px-2 py-2 font-medium"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Sign In
                    </Link>
                    <Link
                      href="/auth/signup"
                      className="bg-gradient-to-r from-blue-600 to-pink-600 text-white px-4 py-3 rounded-lg hover:from-pink-600 hover:to-purple-600 transition-all duration-300 flex items-center space-x-2 w-fit font-medium"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <User className="h-4 w-4" />
                      <span>Get Started</span>
                    </Link>
                  </>
                )}
              </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </nav>
    </header>
  );
}
